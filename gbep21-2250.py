from datetime import datetime
import dazl
import asyncio
import csv
from dazl.ledger.api_types import CreateEvent 
import sys

import pytz
# gambyl_token=
qa_url = 'https://qa-exchange-gambyl.daml.app/'
dit_url = 'https://dit-exchange-gambyl.daml.app/'
prod_url = 'https://prod-exchange-gambyl.daml.app/'

async def main():
    token = '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
    async with dazl.connect(url=prod_url, oauth_token=token) as conn:
        print("Collecting data")
        results = await get_existing_users_data(conn)
        
        today = datetime.today().astimezone(pytz.timezone('US/Eastern'))
        
        filename = f'existing_users_data_{today}.csv'
        
        keys = results[0].keys()
    
        with open(filename, "w", newline="") as csvfile:
            dict_writer = csv.DictWriter(csvfile, keys)
            dict_writer.writeheader()
            dict_writer.writerows(results)
        
        print("Csv was created")

async def get_length_by_template(conn, template, filter = {}):
    async with conn.query(template, filter) as stream:
        list_of_results = [i async for i in stream.creates()]
        return len(list_of_results)

async def get_list_by_template(conn, template, filter = {}):
    async with conn.query(template, filter) as stream:
        list_of_results = [i async for i in stream.creates() if isinstance(i, CreateEvent)]
        return list_of_results

async def get_existing_users_data(conn):
    results = []
    async with conn.query('Gambyl.Gambling.Account.Model:Account') as account_stream:
        async for account in account_stream:
            if not isinstance(account, CreateEvent):
                continue
            unverifiedIdentityList = await get_list_by_template(conn,'Gambyl.Gambling.Identity.Model:GamblerUnverifiedIdentity', {'customer': account.payload['customer']})
            if len(unverifiedIdentityList) == 0:
                continue
            existing_user_data = dict()
            existing_user_data['first_name'] = unverifiedIdentityList[0].payload['firstName']
            existing_user_data['last_name'] = unverifiedIdentityList[0].payload['lastName']
            existing_user_data['email'] = unverifiedIdentityList[0].payload['emailAddress']
            existing_user_data['phone_number'] = unverifiedIdentityList[0].payload['phoneNumber']
            existing_user_data['total_funds'] = account.payload['totalMainBalance']
            existing_user_data['available_bonus'] = account.payload['totalBonusBalance']
            existing_user_data['total_fees_paid'] = account.payload['totalFees']
            existing_user_data['available_balance'] = account.payload['totalMainBalance'] + account.payload['totalBonusBalance']
            existing_user_data['pending_withdraws'] = await get_length_by_template(conn, 'MoneyMatrixIntegration.Withdraw:WithdrawRequest', {'customer': account.payload['customer']})
            existing_user_data['pending_wagers'] = await get_length_by_template(conn, 'Gambyl.Gambling.Bet.Model:BetPlacement',{'status': lambda status: status in ["Unmatched", "Matched"], 'customer': account.payload['customer']})

            results.append(existing_user_data)
    return results

asyncio.run(main())
